# Python Functions Course - Exercise Solutions

print("=== Python Functions Course - Exercise Solutions ===\n")

# Exercise 1: Temperature Converter
def celsius_to_fahrenheit(celsius):
    """
    Convert temperature from Celsius to Fahrenheit.
    
    Args:
        celsius (float): Temperature in Celsius
    
    Returns:
        float: Temperature in Fahrenheit
    """
    fahrenheit = (celsius * 9/5) + 32
    return fahrenheit

# Test Exercise 1
print("Exercise 1: Temperature Converter")
print(f"0°C = {celsius_to_fahrenheit(0)}°F")
print(f"25°C = {celsius_to_fahrenheit(25)}°F")
print(f"100°C = {celsius_to_fahrenheit(100)}°F")
print()

# Exercise 2: Grade Calculator
def calculate_average(scores):
    """
    Calculate the average of a list of scores.
    
    Args:
        scores (list): List of numerical scores
    
    Returns:
        float: Average score rounded to 1 decimal place
    """
    if not scores:  # Handle empty list
        return 0
    
    total = sum(scores)
    average = total / len(scores)
    return round(average, 1)

# Test Exercise 2
print("Exercise 2: Grade Calculator")
test_scores = [85, 90, 78, 92, 88]
print(f"Scores: {test_scores}")
print(f"Average: {calculate_average(test_scores)}")
print(f"Empty list average: {calculate_average([])}")
print()

# Exercise 3: Password Validator
def is_strong_password(password):
    """
    Check if a password meets strength requirements.
    
    Requirements:
    - At least 8 characters long
    - Contains at least one uppercase letter
    - Contains at least one lowercase letter
    - Contains at least one digit
    
    Args:
        password (str): Password to validate
    
    Returns:
        bool: True if password is strong, False otherwise
    """
    # Check length
    if len(password) < 8:
        return False
    
    # Check for required character types
    has_upper = any(char.isupper() for char in password)
    has_lower = any(char.islower() for char in password)
    has_digit = any(char.isdigit() for char in password)
    
    return has_upper and has_lower and has_digit

# Test Exercise 3
print("Exercise 3: Password Validator")
test_passwords = [
    "MyPass123",      # Strong
    "mypass123",      # No uppercase
    "MYPASS123",      # No lowercase
    "MyPassword",     # No digit
    "MyP123",         # Too short
    "VeryStrong1"     # Strong
]

for pwd in test_passwords:
    result = is_strong_password(pwd)
    print(f"'{pwd}' -> {'Strong' if result else 'Weak'}")

print("\n=== Bonus Exercises ===\n")

# Bonus Exercise 1: Fibonacci with Recursion
def fibonacci(n):
    """
    Calculate the nth Fibonacci number using recursion.
    
    Args:
        n (int): Position in Fibonacci sequence (0-indexed)
    
    Returns:
        int: The nth Fibonacci number
    """
    if n <= 1:
        return n
    return fibonacci(n - 1) + fibonacci(n - 2)

print("Bonus 1: Fibonacci Sequence")
for i in range(10):
    print(f"F({i}) = {fibonacci(i)}")
print()

# Bonus Exercise 2: Function with *args and **kwargs
def flexible_calculator(operation, *numbers, **options):
    """
    Flexible calculator that can perform various operations.
    
    Args:
        operation (str): Operation to perform ('sum', 'product', 'average')
        *numbers: Variable number of numbers to operate on
        **options: Additional options like 'round_to' for rounding
    
    Returns:
        float: Result of the calculation
    """
    if not numbers:
        return 0
    
    if operation == 'sum':
        result = sum(numbers)
    elif operation == 'product':
        result = 1
        for num in numbers:
            result *= num
    elif operation == 'average':
        result = sum(numbers) / len(numbers)
    else:
        raise ValueError(f"Unknown operation: {operation}")
    
    # Apply rounding if specified
    if 'round_to' in options:
        result = round(result, options['round_to'])
    
    return result

print("Bonus 2: Flexible Calculator")
print(f"Sum: {flexible_calculator('sum', 1, 2, 3, 4, 5)}")
print(f"Product: {flexible_calculator('product', 2, 3, 4)}")
print(f"Average: {flexible_calculator('average', 10, 20, 30, round_to=1)}")
print()

# Bonus Exercise 3: Decorator Example
def timer_decorator(func):
    """
    A simple decorator that prints when a function starts and ends.
    """
    def wrapper(*args, **kwargs):
        print(f"Starting {func.__name__}...")
        result = func(*args, **kwargs)
        print(f"Finished {func.__name__}!")
        return result
    return wrapper

@timer_decorator
def slow_function():
    """A function that simulates some work."""
    import time
    time.sleep(1)
    return "Work completed!"

print("Bonus 3: Decorator Example")
result = slow_function()
print(f"Result: {result}")

print("\n🎉 Great job completing all the exercises! 🎉")
