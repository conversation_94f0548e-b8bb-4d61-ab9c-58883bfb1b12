<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Python Functions - Step by Step Course</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            overflow: hidden;
        }

        .slide-container {
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .slide {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 60px;
            max-width: 900px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            display: none;
            animation: slideIn 0.5s ease-in-out;
        }

        .slide.active {
            display: block;
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        h1 {
            color: #4a5568;
            font-size: 2.5em;
            margin-bottom: 30px;
            text-align: center;
            border-bottom: 3px solid #667eea;
            padding-bottom: 15px;
        }

        h2 {
            color: #2d3748;
            font-size: 2em;
            margin-bottom: 25px;
            text-align: center;
        }

        h3 {
            color: #4a5568;
            font-size: 1.5em;
            margin-bottom: 20px;
            margin-top: 30px;
        }

        p, li {
            font-size: 1.1em;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        ul {
            margin-left: 30px;
            margin-bottom: 20px;
        }

        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 1em;
            margin: 20px 0;
            overflow-x: auto;
            border-left: 4px solid #667eea;
        }

        .highlight {
            background: #ffd700;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }

        .navigation {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 15px;
            z-index: 1000;
        }

        .nav-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .nav-btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .nav-btn:disabled {
            background: #a0aec0;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .slide-counter {
            position: fixed;
            top: 30px;
            right: 30px;
            background: rgba(255,255,255,0.9);
            padding: 10px 20px;
            border-radius: 20px;
            font-weight: bold;
            color: #4a5568;
        }

        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            height: 4px;
            background: #667eea;
            transition: width 0.3s ease;
            z-index: 1001;
        }

        .example-box {
            background: #f7fafc;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .output-box {
            background: #1a202c;
            color: #68d391;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            border-left: 4px solid #68d391;
        }
    </style>
</head>
<body>
    <div class="progress-bar" id="progressBar"></div>
    <div class="slide-counter" id="slideCounter">1 / 20</div>

    <!-- Slide 1: Title Slide -->
    <div class="slide-container">
        <div class="slide active" id="slide1">
            <h1>🐍 Python Functions</h1>
            <h2>A Complete Step-by-Step Course</h2>
            <div style="text-align: center; margin-top: 40px;">
                <p style="font-size: 1.3em; color: #4a5568;">Master the fundamentals of Python functions</p>
                <p style="font-size: 1.1em; margin-top: 20px;">From basic definitions to advanced concepts</p>
            </div>
            <div style="margin-top: 50px;">
                <h3>What You'll Learn:</h3>
                <ul>
                    <li>Function definition and syntax</li>
                    <li>Parameters and arguments</li>
                    <li>Return values and scope</li>
                    <li>Advanced function concepts</li>
                    <li>Best practices and real examples</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Slide 2: What are Functions? -->
    <div class="slide-container">
        <div class="slide" id="slide2">
            <h2>🤔 What are Functions?</h2>
            <p>A <span class="highlight">function</span> is a reusable block of code that performs a specific task.</p>

            <h3>Think of functions like:</h3>
            <ul>
                <li><strong>A recipe</strong> - You give it ingredients (inputs) and get a dish (output)</li>
                <li><strong>A machine</strong> - You put something in, it processes it, and gives you a result</li>
                <li><strong>A tool</strong> - You use it whenever you need to perform that specific task</li>
            </ul>

            <div class="example-box">
                <h3>Real-world analogy:</h3>
                <p>A coffee machine is like a function:</p>
                <ul>
                    <li><strong>Input:</strong> Coffee beans, water, settings</li>
                    <li><strong>Process:</strong> Brewing</li>
                    <li><strong>Output:</strong> A cup of coffee</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Slide 3: Why Use Functions? -->
    <div class="slide-container">
        <div class="slide" id="slide3">
            <h2>🎯 Why Use Functions?</h2>

            <h3>1. Code Reusability</h3>
            <p>Write once, use many times!</p>
            <div class="code-block">
# Without functions - repetitive code
print("Hello, Alice!")
print("Welcome to our program!")
print("Have a great day!")

print("Hello, Bob!")
print("Welcome to our program!")
print("Have a great day!")
            </div>

            <h3>2. Organization</h3>
            <p>Break complex problems into smaller, manageable pieces</p>

            <h3>3. Easier Testing & Debugging</h3>
            <p>Test individual parts of your program separately</p>

            <h3>4. Collaboration</h3>
            <p>Different team members can work on different functions</p>
        </div>
    </div>

    <!-- Slide 4: Function Anatomy -->
    <div class="slide-container">
        <div class="slide" id="slide4">
            <h2>🔍 Function Anatomy</h2>

            <div class="code-block">
def function_name(parameters):
    """Optional docstring"""
    # Function body
    # Code that does something
    return result  # Optional
            </div>

            <h3>Parts of a Function:</h3>
            <ul>
                <li><span class="highlight">def</span> - Keyword that starts function definition</li>
                <li><span class="highlight">function_name</span> - What you call your function</li>
                <li><span class="highlight">parameters</span> - Inputs the function accepts</li>
                <li><span class="highlight">docstring</span> - Description of what the function does</li>
                <li><span class="highlight">function body</span> - The code that runs</li>
                <li><span class="highlight">return</span> - What the function gives back (optional)</li>
            </ul>
        </div>
    </div>

    <!-- Slide 5: Your First Function -->
    <div class="slide-container">
        <div class="slide" id="slide5">
            <h2>🚀 Your First Function</h2>

            <h3>Let's create a simple greeting function:</h3>
            <div class="code-block">
def greet():
    print("Hello, World!")

# Call the function
greet()
            </div>

            <div class="output-box">
Output: Hello, World!
            </div>

            <h3>Step by step:</h3>
            <ul>
                <li><strong>Line 1:</strong> Define function named 'greet' with no parameters</li>
                <li><strong>Line 2:</strong> Function body - what it does when called</li>
                <li><strong>Line 4:</strong> Call the function by using its name with parentheses</li>
            </ul>

            <div class="example-box">
                <p><strong>Important:</strong> Functions must be defined before they can be called!</p>
            </div>
        </div>
    </div>

    <!-- Slide 6: Function with Parameters -->
    <div class="slide-container">
        <div class="slide" id="slide6">
            <h2>📥 Functions with Parameters</h2>

            <p>Parameters allow functions to accept input values:</p>

            <div class="code-block">
def greet_person(name):
    print(f"Hello, {name}!")

# Call with different arguments
greet_person("Alice")
greet_person("Bob")
greet_person("Charlie")
            </div>

            <div class="output-box">
Output:
Hello, Alice!
Hello, Bob!
Hello, Charlie!
            </div>

            <h3>Key Terms:</h3>
            <ul>
                <li><span class="highlight">Parameter</span> - Variable in function definition (name)</li>
                <li><span class="highlight">Argument</span> - Actual value passed when calling ("Alice")</li>
            </ul>
        </div>
    </div>

    <!-- Slide 7: Multiple Parameters -->
    <div class="slide-container">
        <div class="slide" id="slide7">
            <h2>📊 Multiple Parameters</h2>

            <p>Functions can accept multiple parameters:</p>

            <div class="code-block">
def introduce_person(name, age, city):
    print(f"Hi! I'm {name}, I'm {age} years old, and I live in {city}.")

# Call with multiple arguments
introduce_person("Sarah", 25, "New York")
introduce_person("Mike", 30, "London")
            </div>

            <div class="output-box">
Output:
Hi! I'm Sarah, I'm 25 years old, and I live in New York.
Hi! I'm Mike, I'm 30 years old, and I live in London.
            </div>

            <div class="example-box">
                <h3>Order Matters!</h3>
                <p>Arguments are matched to parameters by position:</p>
                <div class="code-block">
introduce_person("Boston", 28, "Emma")  # Wrong order!
                </div>
                <div class="output-box">
Output: Hi! I'm Boston, I'm 28 years old, and I live in Emma.
                </div>
            </div>
        </div>
    </div>

    <!-- Slide 8: Return Values -->
    <div class="slide-container">
        <div class="slide" id="slide8">
            <h2>↩️ Return Values</h2>

            <p>Functions can return values using the <span class="highlight">return</span> statement:</p>

            <div class="code-block">
def add_numbers(a, b):
    result = a + b
    return result

# Store the returned value
sum_result = add_numbers(5, 3)
print(f"The sum is: {sum_result}")

# Use return value directly
print(f"Double sum: {add_numbers(10, 20) * 2}")
            </div>

            <div class="output-box">
Output:
The sum is: 8
Double sum: 60
            </div>

            <h3>Key Points:</h3>
            <ul>
                <li>Functions without <code>return</code> return <code>None</code></li>
                <li>You can return any data type</li>
                <li>Return immediately exits the function</li>
            </ul>
        </div>
    </div>

    <!-- Slide 9: Default Parameters -->
    <div class="slide-container">
        <div class="slide" id="slide9">
            <h2>🎛️ Default Parameters</h2>

            <p>Give parameters default values to make them optional:</p>

            <div class="code-block">
def greet_with_title(name, title="Mr./Ms."):
    return f"Hello, {title} {name}!"

# Call with both arguments
print(greet_with_title("Smith", "Dr."))

# Call with just required argument
print(greet_with_title("Johnson"))

# Call using keyword arguments
print(greet_with_title("Brown", title="Prof."))
            </div>

            <div class="output-box">
Output:
Hello, Dr. Smith!
Hello, Mr./Ms. Johnson!
Hello, Prof. Brown!
            </div>

            <div class="example-box">
                <p><strong>Rule:</strong> Default parameters must come after non-default parameters!</p>
            </div>
        </div>
    </div>

    <!-- Slide 10: Keyword Arguments -->
    <div class="slide-container">
        <div class="slide" id="slide10">
            <h2>🏷️ Keyword Arguments</h2>

            <p>Call functions using parameter names for clarity:</p>

            <div class="code-block">
def create_profile(name, age, city, occupation="Student"):
    return f"{name}, {age}, from {city}, works as {occupation}"

# Positional arguments
print(create_profile("Alice", 22, "Paris"))

# Keyword arguments (order doesn't matter!)
print(create_profile(city="Tokyo", name="Bob", age=28, occupation="Engineer"))

# Mix of both (positional first!)
print(create_profile("Carol", age=35, city="Berlin", occupation="Doctor"))
            </div>

            <div class="output-box">
Output:
Alice, 22, from Paris, works as Student
Bob, 28, from Tokyo, works as Engineer
Carol, 35, from Berlin, works as Doctor
            </div>
        </div>
    </div>

    <!-- Slide 11: *args - Variable Arguments -->
    <div class="slide-container">
        <div class="slide" id="slide11">
            <h2>📦 *args - Variable Arguments</h2>

            <p>Accept any number of positional arguments:</p>

            <div class="code-block">
def sum_all(*numbers):
    total = 0
    for num in numbers:
        total += num
    return total

# Call with different numbers of arguments
print(sum_all(1, 2, 3))
print(sum_all(10, 20, 30, 40, 50))
print(sum_all(5))
print(sum_all())  # No arguments
            </div>

            <div class="output-box">
Output:
6
150
5
0
            </div>

            <div class="example-box">
                <p><strong>*args</strong> collects extra positional arguments into a tuple</p>
            </div>
        </div>
    </div>

    <!-- Slide 12: **kwargs - Keyword Arguments -->
    <div class="slide-container">
        <div class="slide" id="slide12">
            <h2>🗝️ **kwargs - Keyword Arguments</h2>

            <p>Accept any number of keyword arguments:</p>

            <div class="code-block">
def create_user(**user_info):
    print("User Information:")
    for key, value in user_info.items():
        print(f"  {key}: {value}")

# Call with different keyword arguments
create_user(name="Alice", age=25, city="Boston")
print()
create_user(name="Bob", occupation="Engineer", hobby="Photography")
            </div>

            <div class="output-box">
Output:
User Information:
  name: Alice
  age: 25
  city: Boston

User Information:
  name: Bob
  occupation: Engineer
  hobby: Photography
            </div>

            <div class="example-box">
                <p><strong>**kwargs</strong> collects extra keyword arguments into a dictionary</p>
            </div>
        </div>
    </div>

    <!-- Slide 13: Combining All Parameter Types -->
    <div class="slide-container">
        <div class="slide" id="slide13">
            <h2>🎯 Combining Parameter Types</h2>

            <p>You can use all parameter types together (in this order):</p>

            <div class="code-block">
def flexible_function(required, default_param="default", *args, **kwargs):
    print(f"Required: {required}")
    print(f"Default: {default_param}")
    print(f"Args: {args}")
    print(f"Kwargs: {kwargs}")

# Example calls
flexible_function("must_have")
print()
flexible_function("must_have", "custom", 1, 2, 3, name="Alice", age=30)
            </div>

            <div class="output-box">
Output:
Required: must_have
Default: default
Args: ()
Kwargs: {}

Required: must_have
Default: custom
Args: (1, 2, 3)
Kwargs: {'name': 'Alice', 'age': 30}
            </div>
        </div>
    </div>

    <!-- Slide 14: Variable Scope -->
    <div class="slide-container">
        <div class="slide" id="slide14">
            <h2>🔍 Variable Scope</h2>

            <p>Scope determines where variables can be accessed:</p>

            <div class="code-block">
# Global variable
global_var = "I'm global!"

def my_function():
    # Local variable
    local_var = "I'm local!"
    print(f"Inside function: {global_var}")
    print(f"Inside function: {local_var}")

my_function()
print(f"Outside function: {global_var}")
# print(local_var)  # This would cause an error!
            </div>

            <div class="output-box">
Output:
Inside function: I'm global!
Inside function: I'm local!
Outside function: I'm global!
            </div>

            <h3>Scope Rules:</h3>
            <ul>
                <li><strong>Local:</strong> Variables inside functions</li>
                <li><strong>Global:</strong> Variables outside all functions</li>
                <li>Local variables "shadow" global ones with the same name</li>
            </ul>
        </div>
    </div>

    <!-- Slide 15: Global Keyword -->
    <div class="slide-container">
        <div class="slide" id="slide15">
            <h2>🌍 The global Keyword</h2>

            <p>Modify global variables inside functions:</p>

            <div class="code-block">
counter = 0  # Global variable

def increment():
    global counter  # Tell Python we want to modify the global
    counter += 1
    print(f"Counter is now: {counter}")

def increment_local():
    counter = 10  # This creates a new local variable!
    print(f"Local counter: {counter}")

print(f"Initial counter: {counter}")
increment()
increment()
increment_local()
print(f"Final global counter: {counter}")
            </div>

            <div class="output-box">
Output:
Initial counter: 0
Counter is now: 1
Counter is now: 2
Local counter: 10
Final global counter: 2
            </div>
        </div>
    </div>

    <!-- Slide 16: Return Multiple Values -->
    <div class="slide-container">
        <div class="slide" id="slide16">
            <h2>📤 Returning Multiple Values</h2>

            <p>Python functions can return multiple values:</p>

            <div class="code-block">
def get_name_parts(full_name):
    parts = full_name.split()
    first_name = parts[0]
    last_name = parts[-1]
    return first_name, last_name  # Return tuple

def calculate_stats(numbers):
    return min(numbers), max(numbers), sum(numbers) / len(numbers)

# Unpack returned values
first, last = get_name_parts("John Doe Smith")
print(f"First: {first}, Last: {last}")

# Get all values at once
minimum, maximum, average = calculate_stats([1, 2, 3, 4, 5])
print(f"Min: {minimum}, Max: {maximum}, Avg: {average}")
            </div>

            <div class="output-box">
Output:
First: John, Last: Smith
Min: 1, Max: 5, Avg: 3.0
            </div>
        </div>
    </div>

    <!-- Slide 17: Lambda Functions -->
    <div class="slide-container">
        <div class="slide" id="slide17">
            <h2>⚡ Lambda Functions</h2>

            <p>Lambda functions are small, anonymous functions for simple operations:</p>

            <div class="code-block">
# Regular function
def square(x):
    return x ** 2

# Lambda function (same thing!)
square_lambda = lambda x: x ** 2

print(square(5))
print(square_lambda(5))

# Common use with built-in functions
numbers = [1, 2, 3, 4, 5]
squared = list(map(lambda x: x ** 2, numbers))
print(f"Squared: {squared}")

# Filter even numbers
evens = list(filter(lambda x: x % 2 == 0, numbers))
print(f"Evens: {evens}")
            </div>

            <div class="output-box">
Output:
25
25
Squared: [1, 4, 9, 16, 25]
Evens: [2, 4]
            </div>

            <div class="example-box">
                <p><strong>When to use:</strong> Simple, one-line functions that you use once</p>
            </div>
        </div>
    </div>

    <!-- Slide 18: Recursion -->
    <div class="slide-container">
        <div class="slide" id="slide18">
            <h2>🔄 Recursion</h2>

            <p>A function that calls itself to solve smaller versions of the same problem:</p>

            <div class="code-block">
def factorial(n):
    # Base case - stop the recursion
    if n <= 1:
        return 1
    # Recursive case - function calls itself
    else:
        return n * factorial(n - 1)

def countdown(n):
    if n <= 0:
        print("Blast off! 🚀")
    else:
        print(n)
        countdown(n - 1)  # Call itself with smaller number

print(f"5! = {factorial(5)}")
print("\nCountdown:")
countdown(3)
            </div>

            <div class="output-box">
Output:
5! = 120

Countdown:
3
2
1
Blast off! 🚀
            </div>

            <div class="example-box">
                <p><strong>Key:</strong> Always have a base case to stop the recursion!</p>
            </div>
        </div>
    </div>

    <!-- Slide 19: Function Documentation -->
    <div class="slide-container">
        <div class="slide" id="slide19">
            <h2>📚 Function Documentation</h2>

            <p>Good functions have clear documentation (docstrings):</p>

            <div class="code-block">
def calculate_bmi(weight, height):
    """
    Calculate Body Mass Index (BMI).

    Args:
        weight (float): Weight in kilograms
        height (float): Height in meters

    Returns:
        float: BMI value

    Example:
        >>> calculate_bmi(70, 1.75)
        22.86
    """
    if height <= 0:
        raise ValueError("Height must be positive")

    bmi = weight / (height ** 2)
    return round(bmi, 2)

# Access documentation
print(calculate_bmi.__doc__)
print(f"BMI: {calculate_bmi(70, 1.75)}")
            </div>

            <div class="output-box">
Output:
    Calculate Body Mass Index (BMI).

    Args:
        weight (float): Weight in kilograms
        height (float): Height in meters

    Returns:
        float: BMI value

    Example:
        >>> calculate_bmi(70, 1.75)
        22.86

BMI: 22.86
            </div>
        </div>
    </div>

    <!-- Slide 20: Practice Exercises -->
    <div class="slide-container">
        <div class="slide" id="slide20">
            <h2>💪 Practice Exercises</h2>

            <h3>Try these exercises to master functions:</h3>

            <div class="example-box">
                <h4>Exercise 1: Temperature Converter</h4>
                <p>Write a function that converts Celsius to Fahrenheit:</p>
                <div class="code-block">
def celsius_to_fahrenheit(celsius):
    # Your code here
    pass

# Test: celsius_to_fahrenheit(0) should return 32
                </div>
            </div>

            <div class="example-box">
                <h4>Exercise 2: Grade Calculator</h4>
                <p>Create a function that takes a list of scores and returns the average:</p>
                <div class="code-block">
def calculate_average(scores):
    # Your code here
    pass

# Test: calculate_average([85, 90, 78, 92, 88]) should return 86.6
                </div>
            </div>

            <div class="example-box">
                <h4>Exercise 3: Password Validator</h4>
                <p>Write a function that checks if a password is strong:</p>
                <div class="code-block">
def is_strong_password(password):
    # Check: at least 8 chars, has uppercase, lowercase, digit
    # Your code here
    pass

# Test: is_strong_password("MyPass123") should return True
                </div>
            </div>

            <h3>🎉 Congratulations!</h3>
            <p>You've completed the Python Functions course! Keep practicing and building amazing things!</p>
        </div>
    </div>

    <!-- Navigation -->
    <div class="navigation">
        <button class="nav-btn" id="prevBtn" onclick="changeSlide(-1)">← Previous</button>
        <button class="nav-btn" id="nextBtn" onclick="changeSlide(1)">Next →</button>
    </div>

    <script>
        let currentSlide = 1;
        const totalSlides = 20; // Will be updated as we add more slides

        function updateSlideCounter() {
            document.getElementById('slideCounter').textContent = `${currentSlide} / ${totalSlides}`;
            document.getElementById('progressBar').style.width = `${(currentSlide / totalSlides) * 100}%`;
        }

        function changeSlide(direction) {
            const current = document.getElementById(`slide${currentSlide}`);
            current.classList.remove('active');
            
            currentSlide += direction;
            
            if (currentSlide < 1) currentSlide = 1;
            if (currentSlide > totalSlides) currentSlide = totalSlides;
            
            const next = document.getElementById(`slide${currentSlide}`);
            if (next) {
                next.classList.add('active');
            }
            
            updateSlideCounter();
            updateNavigationButtons();
        }

        function updateNavigationButtons() {
            document.getElementById('prevBtn').disabled = currentSlide === 1;
            document.getElementById('nextBtn').disabled = currentSlide === totalSlides;
        }

        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') changeSlide(-1);
            if (e.key === 'ArrowRight') changeSlide(1);
        });

        // Initialize
        updateSlideCounter();
        updateNavigationButtons();
    </script>
</body>
</html>
